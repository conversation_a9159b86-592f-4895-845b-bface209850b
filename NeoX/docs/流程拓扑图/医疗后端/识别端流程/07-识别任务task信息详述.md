```mermaid
flowchart TD
    subgraph "Core Data Models 核心数据模型"
        A1[PTask<br/>p_task表<br/>处方识别任务主表]
        A2[PTaskFailed<br/>p_task_failed表<br/>失败任务记录表]
        A3[PrescriptionOriginal<br/>prescription_original表<br/>处方结果表]
        A4[TempTask<br/>p_task_temp表<br/>临时任务分发表]
    end

    subgraph "PTask Fields PTask字段"
        B1[基础信息<br/>merchantId, deviceId<br/>clientType, originFileName]
        B2[任务状态<br/>status, processTime<br/>qrFlag, endFlag]
        B3[文件信息<br/>imagePath, imageList<br/>page, pageCount]
        B4[识别结果<br/>prescriptionOriginalId<br/>qrData, rotate]
        B5[合并信息<br/>taskIds, parentId<br/>timestampList]
        B6[调度信息<br/>qpsId, processId<br/>pretreatment]
        B7[软删除<br/>valid字段<br/>0=删除, 1=有效]
    end

    subgraph "PTaskFailed Fields 失败任务字段"
        C1[失败原因<br/>failedReason<br/>详细错误信息]
        C2[失败类型<br/>status负值<br/>-1,-2,-3,-4]
        C3[预处理信息<br/>pretreatment<br/>客户端预处理结果]
        C4[任务基础信息<br/>继承自PTask<br/>部分字段]
    end

    subgraph "Data Flow 数据流转"
        D1[apply创建PTask<br/>status=WAIT_UPLOAD]
        D2[uploadFile更新<br/>status=WAIT_RECOGNIZE]
        D3[Dispatcher调度<br/>写入p_task_temp]
        D4[识别进程处理<br/>更新状态]
        D5{识别结果}
        D6[成功: 更新PTask<br/>status=RECOGNIZED]
        D7[失败: 转移到PTaskFailed<br/>PTask.valid=0]
        D8[合并完成<br/>创建PrescriptionOriginal]
    end

    A1 --> B1
    A1 --> B2
    A1 --> B3
    A1 --> B4
    A1 --> B5
    A1 --> B6
    A1 --> B7

    A2 --> C1
    A2 --> C2
    A2 --> C3
    A2 --> C4

    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> D5
    D5 --> D6
    D5 --> D7
    D6 --> D8

    D1 --> A1
    D2 --> A1
    D6 --> A1
    D7 --> A1
    D7 --> A2
    D8 --> A3
    D3 --> A4

    style A1 fill:#6a1b9a,stroke:#fff,stroke-width:2px,color:#fff
    style A2 fill:#b71c1c,stroke:#fff,stroke-width:2px,color:#fff
    style A3 fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
    style A4 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style D1 fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style D6 fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
    style D7 fill:#b71c1c,stroke:#fff,stroke-width:2px,color:#fff
    style D8 fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
    style C1 fill:#b71c1c,stroke:#fff,stroke-width:2px,color:#fff
    style C2 fill:#b71c1c,stroke:#fff,stroke-width:2px,color:#fff
```
