```mermaid
flowchart TD
    subgraph "Client Layer 客户端层"
        C1[PC客户端<br/>Windows App]
        C2[Mobile客户端<br/>移动应用]
        C3[API客户端<br/>第三方集成]
        C4[Web客户端<br/>浏览器]
    end

    subgraph "API Gateway 接口网关"
        A1[PrescriptionAsyncController<br/>处方异步控制器]
        A2[apply - 创建任务]
        A3[uploadFile - 上传图片]
        A4[qrListIds - 获取结果]
        A5[qrNoticeDownloaded - 确认下载]
    end

    subgraph "Task Processing 任务处理层"
        T1[PrescriptionAsyncDispatcher<br/>任务调度器]
        T2[PrescriptionAsyncRecognize<br/>识别进程集群]
        T3[PrescriptionAsyncMerge<br/>合并进程]
        T4[ProcessPool<br/>进程池管理]
        T5[QPS Lock<br/>频率控制]
    end

    subgraph "Recognition Engine 识别引擎"
        E1[OCR引擎<br/>文字识别]
        E2[QR引擎<br/>二维码识别]
        E3[JahisParser<br/>JAHIS格式解析]
        E4[AI纠错<br/>智能纠错]
        E5[GPU服务<br/>GPU加速识别]
    end

    subgraph "Data Storage 数据存储"
        D1[MongoDB<br/>p_task 任务表<br/>p_task_failed 失败表<br/>prescription_original 处方表]
        D2[Redis<br/>任务状态缓存<br/>识别结果缓存<br/>轮询时间戳]
        D3[Temporary Storage<br/>p_task_temp 临时表<br/>按进程ID分组]
        D4[File Storage<br/>图片文件存储<br/>云存储]
    end

    subgraph "Message Queue 消息队列"
        M1[RabbitMQ<br/>任务分发]
        M2[Event Bus<br/>事件总线]
        M3[PrescriptionMergedEvent<br/>处方合并事件]
    end

    subgraph "External Services 外部服务"
        S1[Smart Patient Master<br/>患者主数据]
        S2[NSIPS<br/>处方信息系统]
        S3[云存储服务<br/>图片归档]
        S4[监控告警<br/>Sentry]
    end

    C1 --> A1
    C2 --> A1
    C3 --> A1
    C4 --> A1

    A1 --> A2
    A1 --> A3
    A1 --> A4
    A1 --> A5

    A2 --> T1
    A3 --> T1
    T1 --> T2
    T1 --> T4
    T1 --> T5
    T2 --> T3

    T2 --> E1
    T2 --> E2
    T2 --> E3
    T2 --> E4
    T2 --> E5

    T1 --> D1
    T2 --> D1
    T3 --> D1
    T1 --> D2
    T2 --> D2
    T3 --> D2
    T1 --> D3
    T2 --> D3
    A3 --> D4

    T1 --> M1
    T2 --> M1
    T3 --> M2
    T3 --> M3

    T3 --> S1
    T3 --> S2
    T3 --> S3
    T2 --> S4

    A4 --> D2
    A5 --> D2

    style C1 fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style C2 fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style C3 fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style T1 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style T2 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style T3 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style E1 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style E2 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style D1 fill:#6a1b9a,stroke:#fff,stroke-width:2px,color:#fff
    style D2 fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
    style D3 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style D4 fill:#6a1b9a,stroke:#fff,stroke-width:2px,color:#fff
```
