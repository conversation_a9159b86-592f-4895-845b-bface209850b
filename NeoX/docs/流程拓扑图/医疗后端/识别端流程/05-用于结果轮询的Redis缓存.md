```mermaid
flowchart TD
    subgraph "Redis Data Structure Redis数据结构"
        R1["PrescriptionAsyncRecognizedMsgPC:{merchantId}<br/>PC端识别结果消息"]
        R2["PrescriptionAsyncRecognizedMsgAPI:{merchantId}<br/>API端识别结果消息"]
        R3["PrescriptionAsyncNum:{merchantId}<br/>消息计数"]
        R4["CleanMerchantRecognizeResult:{merchantId}<br/>清理锁"]
        R5["Task Status Cache<br/>任务状态缓存"]
    end

    subgraph "Client Polling 客户端轮询"
        C1[客户端启动轮询<br/>FetchStatusTimeStamp=0]
        C2[调用qrListIds<br/>传入timestamp]
        C3[Redis按时间戳查询<br/>getRedisTaskByTimestamp]
        C4[返回结果<br/>qrList, merge, finish]
        C5[更新时间戳<br/>lastTimestamp += 1]
        C6[客户端处理结果<br/>显示给用户]
        C7[确认下载<br/>qrNoticeDownloaded]
    end

    subgraph "Result Processing 结果处理"
        P1{识别状态判断}
        P2[QR_LIST_STATUS_SUCCESS<br/>成功完成]
        P3[QR_LIST_STATUS_QR<br/>QR识别完成]
        P4[QR_LIST_STATUS_DOING<br/>处理中]
        P5[QR_LIST_STATUS_FAILED<br/>识别失败]
        P6[QR_LIST_STATUS_MERGED<br/>已合并]
    end

    subgraph "Cleanup Logic 清理逻辑"
        L1[检查过期时间<br/>超过4天的结果]
        L2[多设备店铺<br/>仅删除过期结果]
        L3[单设备店铺<br/>删除已确认+过期结果]
        L4[deleteRedisTaskByIds<br/>批量删除]
    end

    C1 --> C2
    C2 --> C3
    C3 --> R5
    R5 --> P1
    P1 --> P2
    P1 --> P3
    P1 --> P4
    P1 --> P5
    P1 --> P6
    P2 --> C4
    P3 --> C4
    P4 --> C2
    P5 --> C4
    P6 --> C4
    C4 --> C5
    C5 --> C6
    C6 --> C7
    C7 --> L1
    L1 --> L2
    L1 --> L3
    L2 --> L4
    L3 --> L4
    L4 --> R1
    L4 --> R2

    style C1 fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style C2 fill:#1565c0,stroke:#fff,stroke-width:2px,color:#fff
    style C3 fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
    style C4 fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
    style P2 fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
    style P3 fill:#2e7d32,stroke:#fff,stroke-width:2px,color:#fff
    style P4 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style P5 fill:#b71c1c,stroke:#fff,stroke-width:2px,color:#fff
    style P6 fill:#ef6c00,stroke:#fff,stroke-width:2px,color:#fff
    style L4 fill:#c62828,stroke:#fff,stroke-width:2px,color:#fff
```
